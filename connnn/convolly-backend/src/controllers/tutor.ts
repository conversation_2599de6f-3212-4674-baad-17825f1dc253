import { Request, Response } from "express";
import { Types } from "mongoose";
import { getProfile, getProfileModelByRole } from "../utils/profile";
import { createErrorResponse } from "../middlewares/errorHandler";
import { createOkResponse } from "../utils/misc";
import { endOfPeriod, startOfPeriod } from "../utils/datetime";
import TransactionModel from "../models/transaction.model";
import LessonModel from "../models/lesson.model";
import Student from "../models/student";
import SubscriptionModel from "../models/subscription.model";
import { hashPassword } from "../utils/hashing";
import { FilterQuery } from "mongoose";
import Tutor, { ITutor } from "../models/tutor";
import {
  createTutorCalendars,
  getUserCalendars,
} from "../services/calendarService";
import { logError } from "../utils/logger";
import { Calendar } from "../models/calender";
import { Event } from "../models/Event";
import WithdrawalRequest from "../models/withdrawalRequest.model";
import {
  EmailNotificationService,
  sendTutorAwaitingApprovalMail,
} from "../services/emailNotificationService";
import { TransactionService } from "../services/transactionService";
import { updateProfileHook } from "../hooks/profile";
import Admin from "../models/admin";
import { sendMail } from "../utils/mailer";
import { Review } from "../models/review";

export const getOverviewInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;

    const periodStart = startOfPeriod(period as string);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalEarnings = await TransactionModel.aggregate([
      { $match: { userId: tutorId, createdAt: { $lte: periodEnd } } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);
    const periodEarnings = await TransactionModel.aggregate([
      {
        $match: {
          userId: tutorId,
          createdAt: { $gte: periodStart, $lte: periodEnd },
        },
      },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    const totalLessons = await LessonModel.countDocuments({
      tutorId,
      date: { $lte: periodEnd },
    });
    const periodLessons = await LessonModel.countDocuments({
      tutorId,
      date: { $gte: periodStart, $lte: periodEnd },
    });

    const totalActiveStudents = await LessonModel.distinct("studentId", {
      tutorId,
      date: { $lte: periodEnd },
    }).then((ids) => ids.length);

    const periodActiveStudents = await LessonModel.distinct("studentId", {
      tutorId,
      date: { $gte: periodStart, $lte: periodEnd },
    }).then((ids) => ids.length);

    const totalStudents = await Student.countDocuments({
      createdAt: { $lte: periodEnd },
      tutorId,
    });
    const periodNewStudents = await Student.countDocuments({
      createdAt: { $gte: periodStart, $lte: periodEnd },
      tutorId,
    });

    createOkResponse(res, {
      data: {
        earnings: {
          total: totalEarnings[0]?.total || 0,
          value: periodEarnings[0]?.total || 0,
          percentage: (
            ((periodEarnings[0]?.total || 0) / (totalEarnings[0]?.total || 1)) *
            100
          ).toFixed(2),
        },
        lessons: {
          total: totalLessons,
          value: periodLessons,
          percentage: ((periodLessons / (totalLessons || 1)) * 100).toFixed(2),
        },
        activeStudents: {
          total: totalActiveStudents,
          value: periodActiveStudents,
          percentage: (periodActiveStudents / (totalActiveStudents || 1)) * 100,
        },
        newStudents: {
          total: totalStudents,
          value: periodNewStudents,
          percentage: (periodNewStudents / (totalStudents || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getLessonsInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;
    const days = parseInt(period as string, 10);
    const periodStart = startOfPeriod(days);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalCancelled = await LessonModel.countDocuments({
      tutorId,
      status: "cancelled",
      date: { $lte: periodEnd },
    });
    const periodCancelled = await LessonModel.countDocuments({
      tutorId,
      status: "cancelled",
      date: { $gte: periodStart, $lte: periodEnd },
    });

    const totalScheduled = await LessonModel.countDocuments({
      tutorId,
      status: "scheduled",
      date: { $lte: periodEnd },
    });
    const periodScheduled = await LessonModel.countDocuments({
      tutorId,
      status: "scheduled",
      date: { $gte: periodStart, $lte: periodEnd },
    });

    createOkResponse(res, {
      data: {
        cancelled: {
          total: totalCancelled,
          value: periodCancelled,
          percentage: (periodCancelled / (totalCancelled || 1)) * 100,
        },
        scheduled: {
          total: totalScheduled,
          value: periodScheduled,
          percentage: (periodScheduled / (totalScheduled || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getSubscriptionsInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;
    const days = parseInt(period as string, 10);
    const periodStart = startOfPeriod(days);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalProfileViews = await Student.aggregate([
      { $match: { tutorId, "profileViews.date": { $lte: periodEnd } } },
      { $unwind: "$profileViews" },
      { $match: { "profileViews.date": { $lte: periodEnd } } },
      { $count: "count" },
    ]).then((res) => res[0]?.count || 0);

    const periodProfileViews = await Student.aggregate([
      {
        $match: {
          tutorId,
          "profileViews.date": { $gte: periodStart, $lte: periodEnd },
        },
      },
      { $unwind: "$profileViews" },
      {
        $match: { "profileViews.date": { $gte: periodStart, $lte: periodEnd } },
      },
      { $count: "count" },
    ]).then((res) => res[0]?.count || 0);

    const totalTrialLessons = await LessonModel.countDocuments({
      tutorId,
      type: "trial",
      date: { $lte: periodEnd },
    });
    const periodTrialLessons = await LessonModel.countDocuments({
      tutorId,
      type: "trial",
      date: { $gte: periodStart, $lte: periodEnd },
    });

    const totalSubscriptions = await SubscriptionModel.countDocuments({
      tutorId,
      isActive: true,
      startDate: { $lte: periodEnd },
    });

    const periodSubscriptions = await SubscriptionModel.countDocuments({
      tutorId,
      isActive: true,
      startDate: { $gte: periodStart, $lte: periodEnd },
    });

    createOkResponse(res, {
      data: {
        profileViews: {
          total: totalProfileViews,
          value: periodProfileViews,
          percentage: (periodProfileViews / (totalProfileViews || 1)) * 100,
        },
        trialLessons: {
          total: totalTrialLessons,
          value: periodTrialLessons,
          percentage: (periodTrialLessons / (totalTrialLessons || 1)) * 100,
        },
        newSubscriptions: {
          total: totalSubscriptions,
          value: periodSubscriptions,
          percentage: (periodSubscriptions / (totalSubscriptions || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getTutors = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      subject,
      experienceLevel,
      minPrice,
      maxPrice,
      city,
      state,
      rating,
      gender,
      language,
      availability,
      name,
      page = "1",
      limit = "10",
      sortBy = "createdAt",
      order = "desc",
    } = req.query as Record<string, string>;

    const pageNumber = Math.max(parseInt(page), 1); // Ensures at least page 1
    const limitNumber = Math.max(parseInt(limit), 1); // Ensures at least 1 item per page
    const skip = (pageNumber - 1) * limitNumber;

    const filter: FilterQuery<any> = {};

    // Subject title filter
    if (subject) {
      filter["teachingSubjects.title"] = { $regex: subject, $options: "i" };
    }

    // Experience Level filter
    if (experienceLevel) {
      filter["teachingSubjects.experienceLevel"] = experienceLevel;
    }

    // Price Range filter
    if (minPrice || maxPrice) {
      filter.basePrice = {};
      if (minPrice) filter.basePrice.$gte = Number(minPrice);
      if (maxPrice) filter.basePrice.$lte = Number(maxPrice);
    }

    // City filter
    if (city) {
      filter.city = { $regex: city, $options: "i" };
    }

    // State filter
    if (state) {
      filter.state = { $regex: state, $options: "i" };
    }

    // Rating filter
    if (rating) {
      filter.rating = { $gte: Number(rating) };
    }

    // Gender filter
    if (gender) {
      filter.gender = gender;
    }

    // Language filter
    if (language) {
      filter.languages = { $in: [language] };
    }

    // Availability filter
    if (availability) {
      filter.availability = { $regex: availability, $options: "i" };
    }

    // Name search filter (firstName or lastName)
    if (name) {
      filter.$or = [
        { firstName: { $regex: name, $options: "i" } },
        { lastName: { $regex: name, $options: "i" } },
      ];
    }

    const totalTutors = await Tutor.countDocuments(filter);

    const tutors = await Tutor.find(filter)
      .sort({ [sortBy]: order === "asc" ? 1 : -1 })
      .skip(skip)
      .limit(limitNumber);

    createOkResponse(res, {
      success: true,
      message: "Tutors fetched successfully",
      details: {
        totalTutors,
        totalPages: Math.ceil(totalTutors / limitNumber),
        currentPage: pageNumber,
        limit: limitNumber,
        sortBy,
        sortOrder: order,
      },
      data: tutors,
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

export const updateTutorProfile = async (req: Request, res: Response) => {
  try {
    const updates: Partial<ITutor> = req.body;

    const needReview = [];

    if (updates.aboutMe) needReview.push("aboutMe");

    if (updates.teachingExperience) needReview.push("teachingExperience");

    if (updates.motivatePotentialStudent)
      needReview.push("motivatePotentialStudent");

    if (updates.headline) needReview.push("headline");

    if (updates.introVideo) needReview.push("introVideo");

    if (updates.teachingSubjects) needReview.push("teachingSubjects");

    if (updates.certificates) needReview.push("certificates");

    if (needReview.length) updates.updateApprovalStatus = "pending";

    const { profile, errorDetails } = await updateProfileHook(
      Tutor,
      updates,
      req.user,
      true
    );

    if (needReview.length)
      await sendTutorAwaitingApprovalMail(profile as ITutor, false);

    createOkResponse(res, {
      details: {
        errorDetails,
        needReview,
      },
      success: !errorDetails,
      message: `Profile updated successfully.`,
      data: profile,
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

// ============================================================================
// LESSON MANAGEMENT FEATURES
// ============================================================================

// Get tutor's lessons with filtering and pagination
export const getTutorLessons = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const {
      page = 1,
      limit = 20,
      status,
      startDate,
      endDate,
      studentId,
      sortBy = "scheduledTime",
      order = "desc",
    } = req.query;

    const pageNumber = Math.max(parseInt(page as string), 1);
    const limitNumber = Math.max(parseInt(limit as string), 1);
    const skip = (pageNumber - 1) * limitNumber;

    const filter: FilterQuery<any> = { tutorId: req.user._id };

    // Apply filters
    if (status) {
      filter.status = status;
    }

    if (studentId) {
      filter.studentId = studentId;
    }

    if (startDate || endDate) {
      filter.scheduledTime = {};
      if (startDate) filter.scheduledTime.$gte = new Date(startDate as string);
      if (endDate) filter.scheduledTime.$lte = new Date(endDate as string);
    }

    const totalLessons = await LessonModel.countDocuments(filter);

    const lessons = await LessonModel.find(filter)
      .populate("studentId", "firstname lastname email")
      .populate("subscriptionId", "planType status")
      .sort({ [sortBy as string]: order === "asc" ? 1 : -1 })
      .skip(skip)
      .limit(limitNumber);

    res.json({
      success: true,
      message: "Lessons fetched successfully",
      data: lessons,
      pagination: {
        totalLessons,
        totalPages: Math.ceil(totalLessons / limitNumber),
        currentPage: pageNumber,
        limit: limitNumber,
        sortBy,
        sortOrder: order,
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Update lesson status (confirm, cancel, complete, etc.)
export const updateLessonStatus = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can update lesson status" });
    }

    const { lessonId } = req.params;
    const { status, notes, cancellationReason } = req.body;

    if (!["confirmed", "cancelled", "completed", "no-show"].includes(status)) {
      return res.status(400).json({
        message:
          "Invalid status. Must be one of: confirmed, cancelled, completed, no-show",
      });
    }

    const lesson = await LessonModel.findOne({
      _id: lessonId,
      tutorId: req.user._id,
    });

    if (!lesson) {
      return res.status(404).json({ message: "Lesson not found" });
    }

    // Update lesson
    lesson.status = status;
    if (notes) lesson.notes = notes;

    if (status === "cancelled") {
      lesson.cancelledAt = new Date();
      if (cancellationReason) lesson.cancellationReason = cancellationReason;
    } else if (status === "confirmed") {
      lesson.confirmedAt = new Date();
    }

    await lesson.save();

    // Send email notification to student about lesson status change
    try {
      const [tutor, student] = await Promise.all([
        Tutor.findById(req.user._id),
        Student.findById(lesson.studentId),
      ]);

      if (
        tutor &&
        student &&
        (status === "cancelled" || status === "confirmed")
      ) {
        if (status === "cancelled") {
          await EmailNotificationService.sendStudentLessonStatus(
            {
              id: (student._id as Types.ObjectId).toString(),
              firstname: student.firstname,
              lastname: student.lastname,
              email: student.email,
            },
            {
              id: (tutor._id as Types.ObjectId).toString(),
              firstname: tutor.firstname,
              lastname: tutor.lastname,
              email: tutor.email,
            },
            "cancelled"
          );
        } else if (status === "confirmed") {
          await EmailNotificationService.sendLessonConfirmation(
            {
              id: (student._id as Types.ObjectId).toString(),
              firstname: student.firstname,
              lastname: student.lastname,
              email: student.email,
            },
            {
              id: (tutor._id as Types.ObjectId).toString(),
              firstname: tutor.firstname,
              lastname: tutor.lastname,
              email: tutor.email,
            },
            {
              id: (lesson._id as Types.ObjectId).toString(),
              title: lesson.title || "Tutoring Session",
              scheduledTime: lesson.scheduledTime,
              duration: lesson.duration,
              joinLink: `${process.env.FRONTEND_URL}/classroom/${lesson._id}`,
              status: lesson.status,
            }
          );
        }

        console.log(
          `Email notification sent for lesson status update: ${status}`
        );
      }
    } catch (emailError: any) {
      console.error(
        "Error sending lesson status email notification:",
        emailError
      );
      // Don't fail the status update if email sending fails
    }

    // If lesson is completed, process payment and apply 20% platform fee
    if (status === "completed" && lesson.status !== "completed") {
      const tutor = await Tutor.findById(req.user._id);
      const student = await Student.findById(lesson.studentId);

      if (tutor && student) {
        // Calculate lesson price and platform fee (20%)
        const lessonPriceInCents = Math.round(tutor.basePrice * 100); // Convert to cents
        const platformFeeRate = 0.2; // 20% platform fee
        const platformFeeAmount = Math.round(
          lessonPriceInCents * platformFeeRate
        );
        const tutorEarnings = lessonPriceInCents - platformFeeAmount; // 80% for tutor

        // Update tutor's total lessons and earnings (after platform fee)
        tutor.totalLessons += 1;
        tutor.availableBalance += tutorEarnings;
        await tutor.save();

        // Record the lesson payment and platform fee using transaction service
        try {
          await TransactionService.recordLessonPayment(
            lesson._id as Types.ObjectId,
            student._id as Types.ObjectId,
            tutor._id as Types.ObjectId,
            lessonPriceInCents,
            lesson.isFreeTrial
          );

          console.log(
            `Lesson completed: Tutor ${tutor._id} earned $${
              tutorEarnings / 100
            } (after 20% platform fee) for lesson ${lesson._id}`
          );
        } catch (transactionError: any) {
          console.error(
            "Error recording lesson payment transaction:",
            transactionError
          );
          // Don't fail the lesson completion if transaction recording fails
        }
      }
    }

    res.json({
      success: true,
      message: "Lesson status updated successfully",
      data: lesson,
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// ============================================================================
// AVAILABILITY MANAGEMENT FEATURES
// ============================================================================

// Get tutor's calendars and availability
export const getTutorCalendars = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const calendars = await getUserCalendars(req.user._id, "Tutor");

    res.json({
      success: true,
      message: "Calendars fetched successfully",
      data: calendars,
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Get tutor's schedule/events for a specific period
export const getTutorSchedule = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const { startDate, endDate, calendarId } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        message: "startDate and endDate are required",
      });
    }

    // Get tutor's calendars
    const calendars = await getUserCalendars(req.user._id, "Tutor");

    if (calendars.length === 0) {
      return res.json({
        success: true,
        message: "No calendars found",
        data: [],
      });
    }

    // Filter by specific calendar if provided
    const calendarIds = calendarId
      ? [calendarId]
      : calendars.map((cal) => cal._id);

    // Get events for the specified period
    const events = await Event.find({
      calendarId: { $in: calendarIds },
      startDateTime: {
        $gte: new Date(startDate as string),
        $lte: new Date(endDate as string),
      },
    })
      .populate("calendarId", "name color")
      .sort({ startDateTime: 1 });

    res.json({
      success: true,
      message: "Schedule fetched successfully",
      data: {
        calendars,
        events,
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Create availability slots (recurring or one-time)
export const createAvailabilitySlot = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can create availability slots" });
    }

    const {
      calendarId,
      title = "Available for Booking",
      startDateTime,
      endDateTime,
      isRecurring = false,
      recurrencePattern,
      description,
    } = req.body;

    if (!startDateTime || !endDateTime) {
      return res.status(400).json({
        message: "startDateTime and endDateTime are required",
      });
    }

    // Verify calendar belongs to tutor
    const calendar = await Calendar.findOne({
      _id: calendarId || undefined,
      ownerUserId: req.user.id,
      ownerType: "Tutor",
    });

    if (!calendar && calendarId) {
      return res.status(404).json({ message: "Calendar not found" });
    }

    // Use first calendar if none specified
    let targetCalendar = calendar;
    if (!targetCalendar) {
      const calendars = await getUserCalendars(req.user.id, "Tutor");
      if (calendars.length === 0) {
        return res.status(400).json({
          message: "No calendars found. Please create a calendar first.",
        });
      }
      targetCalendar = calendars[0];
    }

    // Create the availability event
    const event = new Event({
      calendarId: targetCalendar.id,
      title,
      description: description || "Available time slot for student bookings",
      startDateTime: new Date(startDateTime),
      endDateTime: new Date(endDateTime),
      eventType: "availability",
      isRecurring,
      recurrencePattern: isRecurring ? recurrencePattern : undefined,
    });

    await event.save();

    res.status(201).json({
      success: true,
      message: "Availability slot created successfully",
      data: event,
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// ============================================================================
// STUDENT MANAGEMENT FEATURES
// ============================================================================

// Get tutor's students with their subscription status
export const getTutorStudents = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const {
      page = 1,
      limit = 20,
      status,
      sortBy = "createdAt",
      order = "desc",
    } = req.query;

    const pageNumber = Math.max(parseInt(page as string), 1);
    const limitNumber = Math.max(parseInt(limit as string), 1);
    const skip = (pageNumber - 1) * limitNumber;

    // Get students who have subscriptions with this tutor
    const subscriptionFilter: any = { tutorId: req.user._id };
    if (status) {
      subscriptionFilter.status = status;
    }

    const subscriptions = await SubscriptionModel.find(subscriptionFilter)
      .populate({
        path: "studentId",
        select:
          "firstname lastname email createdAt totalLessons hasUsedFreeTrial",
      })
      .sort({ [sortBy as string]: order === "asc" ? 1 : -1 })
      .skip(skip)
      .limit(limitNumber);

    // Get lesson counts for each student
    const studentsWithStats = await Promise.all(
      subscriptions.map(async (subscription) => {
        const student = subscription.studentId as any;

        const lessonStats = await LessonModel.aggregate([
          {
            $match: {
              tutorId: new Types.ObjectId(req.user!._id),
              studentId: student._id,
            },
          },
          {
            $group: {
              _id: "$status",
              count: { $sum: 1 },
            },
          },
        ]);

        const stats = lessonStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {} as any);

        return {
          student: {
            _id: student._id,
            firstname: student.firstname,
            lastname: student.lastname,
            email: student.email,
            createdAt: student.createdAt,
            totalLessons: student.totalLessons,
            hasUsedFreeTrial: student.hasUsedFreeTrial,
          },
          subscription: {
            _id: subscription._id,
            planType: subscription.planType,
            status: subscription.status,
            remainingLessons: subscription.remainingLessons,
            startDate: subscription.currentPeriodStart,
            endDate: subscription.currentPeriodEnd,
          },
          lessonStats: {
            total: Object.values(stats).reduce(
              (sum: number, count: unknown) => sum + (count as number),
              0
            ),
            completed: stats.completed || 0,
            scheduled: stats.scheduled || 0,
            cancelled: stats.cancelled || 0,
            confirmed: stats.confirmed || 0,
          },
        };
      })
    );

    const totalStudents = await SubscriptionModel.countDocuments(
      subscriptionFilter
    );

    res.json({
      success: true,
      message: "Students fetched successfully",
      data: studentsWithStats,
      pagination: {
        totalStudents,
        totalPages: Math.ceil(totalStudents / limitNumber),
        currentPage: pageNumber,
        limit: limitNumber,
        sortBy,
        sortOrder: order,
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Get detailed information about a specific student
export const getStudentDetails = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const { studentId } = req.params;

    // Verify the student has a subscription with this tutor
    const subscription = await SubscriptionModel.findOne({
      tutorId: req.user._id,
      studentId,
    }).populate(
      "studentId",
      "firstname lastname email createdAt totalLessons hasUsedFreeTrial learningReasons skillsToImprove"
    );

    if (!subscription) {
      return res.status(404).json({
        message: "Student not found or not subscribed to your services",
      });
    }

    // Get lesson history
    const lessons = await LessonModel.find({
      tutorId: req.user._id,
      studentId,
    })
      .sort({ scheduledTime: -1 })
      .limit(10);

    // Get lesson statistics
    const lessonStats = await LessonModel.aggregate([
      {
        $match: {
          tutorId: new Types.ObjectId(req.user._id),
          studentId: new Types.ObjectId(studentId),
        },
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    const stats = lessonStats.reduce((acc, stat) => {
      acc[stat._id] = stat.count;
      return acc;
    }, {} as any);

    res.json({
      success: true,
      message: "Student details fetched successfully",
      data: {
        student: subscription.studentId,
        subscription,
        recentLessons: lessons,
        lessonStats: {
          total: Object.values(stats).reduce(
            (sum: number, count: unknown) => sum + (count as number),
            0
          ),
          completed: stats.completed || 0,
          scheduled: stats.scheduled || 0,
          cancelled: stats.cancelled || 0,
          confirmed: stats.confirmed || 0,
        },
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// ============================================================================
// EARNINGS AND FINANCIAL MANAGEMENT
// ============================================================================

// Get detailed earnings breakdown
export const getEarningsBreakdown = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const { period = "30", startDate, endDate } = req.query;

    let dateFilter: any = {};

    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate as string),
          $lte: new Date(endDate as string),
        },
      };
    } else {
      const periodStart = startOfPeriod(period as string);
      const periodEnd = endOfPeriod();
      dateFilter = {
        createdAt: {
          $gte: periodStart,
          $lte: periodEnd,
        },
      };
    }

    // Get tutor details
    const tutor = await Tutor.findById(req.user._id);
    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }

    // Get earnings from transactions
    const earningsData = await TransactionModel.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(req.user._id),
          type: { $in: ["lesson_payout", "bonus"] },
          status: "completed",
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: "$type",
          totalAmount: { $sum: "$amount" },
          count: { $sum: 1 },
        },
      },
    ]);

    // Get withdrawal history
    const withdrawals = await WithdrawalRequest.find({
      tutorId: req.user._id,
      ...dateFilter,
    }).sort({ createdAt: -1 });

    // Calculate totals
    const totalEarnings = earningsData.reduce(
      (sum, item) => sum + item.totalAmount,
      0
    );
    const totalWithdrawals = withdrawals
      .filter((w) => w.status === "processed")
      .reduce((sum, w) => sum + w.amount, 0);

    // Get pending withdrawals
    const pendingWithdrawals = await WithdrawalRequest.find({
      tutorId: req.user._id,
      status: "pending",
    });

    const pendingAmount = pendingWithdrawals.reduce(
      (sum, w) => sum + w.amount,
      0
    );

    // Get monthly earnings trend
    const monthlyEarnings = await TransactionModel.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(req.user._id),
          type: { $in: ["lesson_payout", "bonus"] },
          status: "completed",
          createdAt: {
            $gte: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000),
          }, // Last 6 months
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          totalAmount: { $sum: "$amount" },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 },
      },
    ]);

    res.json({
      success: true,
      message: "Earnings breakdown fetched successfully",
      data: {
        currentBalance: {
          available: tutor.availableBalance,
          pending: pendingAmount,
          total: tutor.availableBalance + pendingAmount,
        },
        periodEarnings: {
          total: totalEarnings,
          byType: earningsData,
          withdrawals: totalWithdrawals,
          net: totalEarnings - totalWithdrawals,
        },
        withdrawalHistory: withdrawals.slice(0, 10), // Last 10 withdrawals
        monthlyTrend: monthlyEarnings,
        summary: {
          totalLessonsCompleted: tutor.totalLessons,
          averageEarningPerLesson:
            tutor.totalLessons > 0 ? totalEarnings / tutor.totalLessons : 0,
          basePrice: tutor.basePrice,
        },
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Get transaction history for tutor
export const getTutorTransactions = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const {
      page = 1,
      limit = 20,
      type,
      status,
      startDate,
      endDate,
    } = req.query;

    const pageNumber = Math.max(parseInt(page as string), 1);
    const limitNumber = Math.max(parseInt(limit as string), 1);
    const skip = (pageNumber - 1) * limitNumber;

    const filter: any = { userId: req.user._id };

    if (type) {
      filter.type = type;
    }

    if (status) {
      filter.status = status;
    }

    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate as string);
      if (endDate) filter.createdAt.$lte = new Date(endDate as string);
    }

    const totalTransactions = await TransactionModel.countDocuments(filter);

    const transactions = await TransactionModel.find(filter)
      .populate("relatedUserId", "firstname lastname email")
      .populate("lessonId", "scheduledTime status")
      .populate("withdrawalRequestId")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNumber);

    res.json({
      success: true,
      message: "Transactions fetched successfully",
      data: transactions,
      pagination: {
        totalTransactions,
        totalPages: Math.ceil(totalTransactions / limitNumber),
        currentPage: pageNumber,
        limit: limitNumber,
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// ============================================================================
// PROFILE AND PERFORMANCE ANALYTICS
// ============================================================================

// Get comprehensive tutor performance metrics
export const getPerformanceMetrics = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const { period = "30" } = req.query;
    const periodStart = startOfPeriod(period as string);
    const periodEnd = endOfPeriod();

    const tutor = await Tutor.findById(req.user._id);
    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }

    // Get lesson completion rate
    const lessonStats = await LessonModel.aggregate([
      {
        $match: {
          tutorId: new Types.ObjectId(req.user._id),
          scheduledTime: { $gte: periodStart, $lte: periodEnd },
        },
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    const totalLessons = lessonStats.reduce((sum, stat) => sum + stat.count, 0);
    const completedLessons =
      lessonStats.find((s) => s._id === "completed")?.count || 0;
    const cancelledLessons =
      lessonStats.find((s) => s._id === "cancelled")?.count || 0;
    const noShowLessons =
      lessonStats.find((s) => s._id === "no-show")?.count || 0;

    // Get student retention rate
    const studentRetention = await SubscriptionModel.aggregate([
      {
        $match: {
          tutorId: new Types.ObjectId(req.user._id),
          startDate: { $gte: periodStart, $lte: periodEnd },
        },
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    const activeSubscriptions =
      studentRetention.find((s) => s._id === "active")?.count || 0;
    const totalSubscriptions = studentRetention.reduce(
      (sum, stat) => sum + stat.count,
      0
    );

    // Get response time metrics (if you have messaging system)
    // This would require a messaging/chat model to calculate average response time

    // Get profile view statistics
    const profileViews = await Student.aggregate([
      {
        $match: {
          tutorId: new Types.ObjectId(req.user._id),
          "profileViews.date": { $gte: periodStart, $lte: periodEnd },
        },
      },
      {
        $unwind: "$profileViews",
      },
      {
        $match: {
          "profileViews.date": { $gte: periodStart, $lte: periodEnd },
        },
      },
      {
        $count: "totalViews",
      },
    ]);

    const totalProfileViews = profileViews[0]?.totalViews || 0;

    // Calculate conversion rate (profile views to subscriptions)
    const conversionRate =
      totalProfileViews > 0
        ? (totalSubscriptions / totalProfileViews) * 100
        : 0;

    // Get earnings per lesson
    const earningsData = await TransactionModel.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(req.user._id),
          type: "lesson_payout",
          status: "completed",
          createdAt: { $gte: periodStart, $lte: periodEnd },
        },
      },
      {
        $group: {
          _id: null,
          totalEarnings: { $sum: "$amount" },
          count: { $sum: 1 },
        },
      },
    ]);

    const totalEarnings = earningsData[0]?.totalEarnings || 0;
    const earningsPerLesson =
      completedLessons > 0 ? totalEarnings / completedLessons : 0;

    res.json({
      success: true,
      message: "Performance metrics fetched successfully",
      data: {
        overview: {
          rating: tutor.rating,
          totalLessons: tutor.totalLessons,
          totalEarnings: tutor.availableBalance,
          approvalStatus: tutor.approvalStatus,
          isActive: tutor.isActive,
        },
        periodMetrics: {
          lessonStats: {
            total: totalLessons,
            completed: completedLessons,
            cancelled: cancelledLessons,
            noShow: noShowLessons,
            completionRate:
              totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0,
            cancellationRate:
              totalLessons > 0 ? (cancelledLessons / totalLessons) * 100 : 0,
          },
          studentMetrics: {
            totalSubscriptions,
            activeSubscriptions,
            retentionRate:
              totalSubscriptions > 0
                ? (activeSubscriptions / totalSubscriptions) * 100
                : 0,
          },
          marketingMetrics: {
            profileViews: totalProfileViews,
            conversionRate,
            subscriptionsFromViews: totalSubscriptions,
          },
          financialMetrics: {
            totalEarnings,
            earningsPerLesson,
            averageSessionValue: tutor.basePrice,
          },
        },
        recommendations: generatePerformanceRecommendations({
          completionRate:
            totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0,
          cancellationRate:
            totalLessons > 0 ? (cancelledLessons / totalLessons) * 100 : 0,
          conversionRate,
          rating: tutor.rating,
        }),
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Helper function to generate performance recommendations
const generatePerformanceRecommendations = (metrics: {
  completionRate: number;
  cancellationRate: number;
  conversionRate: number;
  rating: number;
}) => {
  const recommendations = [];

  if (metrics.completionRate < 80) {
    recommendations.push({
      type: "improvement",
      category: "lesson_completion",
      message:
        "Your lesson completion rate is below 80%. Consider improving punctuality and lesson preparation.",
      priority: "high",
    });
  }

  if (metrics.cancellationRate > 15) {
    recommendations.push({
      type: "warning",
      category: "cancellations",
      message:
        "High cancellation rate detected. Review your scheduling practices and communication with students.",
      priority: "medium",
    });
  }

  if (metrics.conversionRate < 5) {
    recommendations.push({
      type: "improvement",
      category: "conversion",
      message:
        "Low profile-to-subscription conversion rate. Consider updating your profile, intro video, or pricing.",
      priority: "medium",
    });
  }

  if (metrics.rating < 4.0) {
    recommendations.push({
      type: "critical",
      category: "rating",
      message:
        "Your rating is below 4.0. Focus on improving lesson quality and student satisfaction.",
      priority: "high",
    });
  }

  if (recommendations.length === 0) {
    recommendations.push({
      type: "success",
      category: "overall",
      message: "Great performance! Keep up the excellent work.",
      priority: "low",
    });
  }

  return recommendations;
};

// ============================================================================
// REVIEW AND RATING MANAGEMENT
// ============================================================================

// Get tutor's reviews and ratings
export const getTutorReviews = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const {
      page = 1,
      limit = 20,
      flagged,
      sortBy = "createdAt",
      order = "desc",
    } = req.query;

    const pageNumber = Math.max(parseInt(page as string), 1);
    const limitNumber = Math.max(parseInt(limit as string), 1);

    // Get tutor to verify existence and get review stats
    const tutor = await Tutor.findById(req.user._id);
    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }

    // Build query for reviews targeting this tutor
    const reviewQuery: any = {
      targetUser: req.user._id,
      targetUserModel: "Tutor",
    };

    // Filter by flagged status if specified
    if (flagged !== undefined) {
      reviewQuery.flagged = flagged === "true";
    }

    // Build sort object
    const sortObject: any = {};
    if (sortBy === "createdAt") {
      sortObject.createdAt = order === "asc" ? 1 : -1;
    } else {
      sortObject.createdAt = -1; // Default sort
    }

    // Calculate skip for pagination
    const skip = (pageNumber - 1) * limitNumber;

    // Get reviews with populated student data
    const [reviews, totalCount] = await Promise.all([
      Review.find(reviewQuery)
        .populate({
          path: "createdBy",
          select: "firstname lastname avatar email",
        })
        .populate({
          path: "lesson",
          select: "title subject",
        })
        .sort(sortObject)
        .skip(skip)
        .limit(limitNumber)
        .lean(),
      Review.countDocuments(reviewQuery),
    ]);

    // Transform reviews to match frontend expectations
    const transformedReviews = reviews.map((review: any) => ({
      id: review._id,
      rating: review.rating,
      comment: review.comment,
      ratingDate: review.createdAt,
      student: {
        name: review.createdBy
          ? `${review.createdBy.firstname} ${review.createdBy.lastname}`
          : "Anonymous",
        avatar: review.createdBy?.avatar || null,
        email: review.createdBy?.email || null,
      },
      lesson: review.lesson,
      flagged: review.flagged || false,
      commentsCount: 0, // This would need to be implemented if comments feature exists
      likesCount: 0, // This would need to be implemented if likes feature exists
    }));

    // Calculate review statistics from tutor's reviewStats
    const reviewStats = tutor.reviewStats || {
      totalReview: 0,
      avgRating: 0,
      totalRating: 0,
      ratingCount: {},
    };

    res.json({
      success: true,
      message: "Reviews fetched successfully",
      data: {
        reviews: transformedReviews,
        statistics: {
          totalReviews: reviewStats.totalReview || 0,
          averageRating: reviewStats.avgRating || 0,
          flaggedReviews: 0, // This would need to be calculated if needed
          unflaggedReviews: reviewStats.totalReview || 0,
        },
      },
      pagination: {
        totalReviews: totalCount,
        totalPages: Math.ceil(totalCount / limitNumber),
        currentPage: pageNumber,
        limit: limitNumber,
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Report a review as inappropriate
export const reportReview = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can report reviews" });
    }

    const { reviewId } = req.params;
    const { reason } = req.body;

    const tutor = await Tutor.findById(req.user._id);
    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }

    const review = tutor.reviews.find(
      (r: any) => r._id.toString() === reviewId
    );
    if (!review) {
      return res.status(404).json({ message: "Review not found" });
    }

    // Mark review as flagged (this would typically trigger admin review)
    review.flagged = true;
    await tutor.save();

    // Here you could also create a report record or send notification to admin
    // For now, we'll just flag the review

    res.json({
      success: true,
      message:
        "Review reported successfully. It will be reviewed by our moderation team.",
      data: { reviewId, reason },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// ============================================================================
// PROFILE MANAGEMENT FEATURES
// ============================================================================

// Get tutor's complete profile with statistics
export const getTutorCompleteProfile = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can access this endpoint" });
    }

    const tutor = await Tutor.findById(req.user._id)
      .populate("withdrawalRequests")
      .populate("subscriptions")
      .populate("lessons");

    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }

    // Get additional statistics
    const totalStudents = await SubscriptionModel.countDocuments({
      tutorId: req.user._id,
      status: "active",
    });

    const totalEarnings = await TransactionModel.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(req.user._id),
          type: { $in: ["lesson_payout", "bonus"] },
          status: "completed",
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$amount" },
        },
      },
    ]);

    const profileCompleteness = calculateProfileCompleteness(tutor);

    res.json({
      success: true,
      message: "Complete profile fetched successfully",
      data: {
        ...tutor.toObject(),
        statistics: {
          totalStudents,
          totalEarnings: totalEarnings[0]?.total || 0,
          profileCompleteness,
        },
      },
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

// Helper function to calculate profile completeness
const calculateProfileCompleteness = (tutor: any) => {
  const requiredFields = [
    "firstname",
    "lastname",
    "email",
    "headline",
    "motivatePotentialStudent",
    "basePrice",
    "teachingSubjects",
    "teachingExperience",
    "introVideo",
  ];

  const optionalFields = [
    "certificates",
    "academics",
    "languages",
    "timezone",
    "availability",
  ];

  let completedRequired = 0;
  let completedOptional = 0;

  requiredFields.forEach((field) => {
    if (
      tutor[field] &&
      (Array.isArray(tutor[field]) ? tutor[field].length > 0 : true)
    ) {
      completedRequired++;
    }
  });

  optionalFields.forEach((field) => {
    if (
      tutor[field] &&
      (Array.isArray(tutor[field]) ? tutor[field].length > 0 : true)
    ) {
      completedOptional++;
    }
  });

  const requiredPercentage = (completedRequired / requiredFields.length) * 100;
  const optionalPercentage = (completedOptional / optionalFields.length) * 100;
  const overallPercentage = requiredPercentage * 0.7 + optionalPercentage * 0.3;

  return {
    overall: Math.round(overallPercentage),
    required: Math.round(requiredPercentage),
    optional: Math.round(optionalPercentage),
    missingRequired: requiredFields.filter(
      (field) =>
        !tutor[field] ||
        (Array.isArray(tutor[field]) && tutor[field].length === 0)
    ),
    missingOptional: optionalFields.filter(
      (field) =>
        !tutor[field] ||
        (Array.isArray(tutor[field]) && tutor[field].length === 0)
    ),
  };
};

/**
 * Get detailed earnings breakdown showing platform fee deductions
 */
export const getEarningsWithPlatformFee = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }

    if (req.user.role !== "tutor") {
      return res
        .status(403)
        .json({ message: "Only tutors can view earnings breakdown" });
    }

    const tutorId = req.user._id;
    const { startDate, endDate, page = 1, limit = 20 } = req.query;

    // Build date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.$gte = new Date(startDate as string);
    }
    if (endDate) {
      dateFilter.$lte = new Date(endDate as string);
    }

    // Get tutor info
    const tutor = await Tutor.findById(tutorId).select(
      "basePrice availableBalance totalLessons"
    );
    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }

    // Get completed lessons with earnings breakdown
    const completedLessonsQuery: any = {
      tutorId,
      status: "completed",
    };

    if (Object.keys(dateFilter).length > 0) {
      completedLessonsQuery.confirmedAt = dateFilter;
    }

    const [completedLessons, totalLessons] = await Promise.all([
      LessonModel.find(completedLessonsQuery)
        .populate("studentId", "firstname lastname email")
        .populate("subscriptionId", "planType monthlyPrice")
        .sort({ confirmedAt: -1 })
        .skip((Number(page) - 1) * Number(limit))
        .limit(Number(limit)),
      LessonModel.countDocuments(completedLessonsQuery),
    ]);

    // Calculate earnings breakdown for each lesson
    const lessonsWithEarnings = completedLessons.map((lesson) => {
      const lessonPrice = tutor.basePrice; // Price per lesson in dollars
      const lessonPriceInCents = Math.round(lessonPrice * 100);
      const platformFeeRate = 0.2; // 20% platform fee
      const platformFeeAmount = Math.round(
        lessonPriceInCents * platformFeeRate
      );
      const tutorEarnings = lessonPriceInCents - platformFeeAmount;

      return {
        id: lesson._id,
        title: lesson.title || "Tutoring Session",
        scheduledTime: lesson.scheduledTime,
        confirmedAt: lesson.confirmedAt,
        duration: lesson.duration,
        isFreeTrial: lesson.isFreeTrial,
        student: lesson.studentId
          ? {
              id: (lesson.studentId as any)._id,
              name: `${(lesson.studentId as any).firstname} ${
                (lesson.studentId as any).lastname
              }`,
              email: (lesson.studentId as any).email,
            }
          : null,
        subscription: lesson.subscriptionId
          ? {
              id: (lesson.subscriptionId as any)._id,
              planType: (lesson.subscriptionId as any).planType,
              monthlyPrice: (lesson.subscriptionId as any).monthlyPrice,
            }
          : null,
        earnings: {
          grossAmount: lessonPrice, // Original lesson price
          platformFeeRate: platformFeeRate * 100, // 20%
          platformFeeAmount: platformFeeAmount / 100, // Platform fee in dollars
          netEarnings: tutorEarnings / 100, // Tutor earnings after fee in dollars
          currency: "USD",
        },
      };
    });

    // Calculate summary statistics
    const totalGrossEarnings = completedLessons.length * tutor.basePrice;
    const totalPlatformFees = totalGrossEarnings * 0.2;
    const totalNetEarnings = totalGrossEarnings - totalPlatformFees;

    // Get transaction history for verification
    const transactions = await TransactionModel.find({
      userId: tutorId,
      type: { $in: ["lesson_payout", "platform_fee"] },
      ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter }),
    })
      .sort({ createdAt: -1 })
      .limit(10);

    res.json({
      success: true,
      data: {
        tutor: {
          id: tutor._id,
          basePrice: tutor.basePrice,
          availableBalance: tutor.availableBalance / 100, // Convert to dollars
          totalLessons: tutor.totalLessons,
        },
        earnings: {
          summary: {
            totalCompletedLessons: totalLessons,
            totalGrossEarnings,
            totalPlatformFees,
            totalNetEarnings,
            platformFeeRate: 20, // 20%
            currency: "USD",
          },
          lessons: lessonsWithEarnings,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: totalLessons,
            pages: Math.ceil(totalLessons / Number(limit)),
          },
        },
        recentTransactions: transactions.map((tx) => ({
          id: tx._id,
          type: tx.type,
          amount: tx.amount / 100, // Convert to dollars
          description: tx.description,
          createdAt: tx.createdAt,
        })),
        platformFeeInfo: {
          rate: 20, // 20%
          description: "Platform fee charged on each successful lesson",
          calculation: "Net Earnings = Gross Amount - (Gross Amount × 20%)",
          example: {
            lessonPrice: tutor.basePrice,
            platformFee: tutor.basePrice * 0.2,
            tutorEarnings: tutor.basePrice * 0.8,
          },
        },
      },
    });
  } catch (error: any) {
    console.error("Error fetching earnings with platform fee:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch earnings breakdown",
      error: error.message,
    });
  }
};
